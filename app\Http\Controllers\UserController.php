<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Auth;

class UserController extends Controller
{
    /**
     * Display a listing of users (Admin only).
     */
    public function index(Request $request)
    {
        $query = User::with('role');

        // Filter by role
        if ($request->filled('role')) {
            $query->whereHas('role', function($q) use ($request) {
                $q->where('name', $request->role);
            });
        }

        // Search by name or email
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $users = $query->orderBy('created_at', 'desc')->paginate(15);
        $roles = Role::all();

        return view('admin.users.index', compact('users', 'roles'));
    }

    /**
     * Display the specified user.
     */
    public function show(User $user)
    {
        $user->load(['role', 'enrollments.course', 'taughtCourses', 'payments']);
        
        return view('admin.users.show', compact('user'));
    }

    /**
     * Toggle user status (activate/deactivate).
     */
    public function toggleStatus(Request $request, User $user)
    {
        // Prevent admin from deactivating themselves
        if ($user->id === Auth::id()) {
            return redirect()->back()->with('error', 'Anda tidak dapat menonaktifkan akun Anda sendiri.');
        }

        // Prevent deactivating other admins (optional security measure)
        if ($user->isAdmin() && Auth::user()->isAdmin()) {
            return redirect()->back()->with('error', 'Tidak dapat mengubah status admin lain.');
        }

        // Toggle the status (assuming we add an 'is_active' field to users table)
        // For now, we'll use a simple approach with email_verified_at
        if ($user->email_verified_at) {
            $user->email_verified_at = null;
            $status = 'dinonaktifkan';
        } else {
            $user->email_verified_at = now();
            $status = 'diaktifkan';
        }

        $user->save();

        return redirect()->back()->with('success', "Pengguna {$user->name} berhasil {$status}.");
    }
}
