<?php $__env->startSection('title', 'Dashboard Siswa - Sistem Kursus'); ?>

<?php $__env->startSection('content'); ?>
<!-- Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 overflow-hidden" style="background: linear-gradient(135deg, var(--dark-teal) 0%, var(--primary-red) 100%); border-radius: 20px;">
            <div class="card-body p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="d-flex align-items-center mb-3">
                            <div class="bg-white rounded-circle p-3 me-3" style="width: 60px; height: 60px;">
                                <i class="fas fa-user-graduate fa-lg" style="color: var(--primary-red);"></i>
                            </div>
                            <div>
                                <h1 class="text-white mb-1 fw-bold">Selamat datang kembali!</h1>
                                <h3 class="text-white-50 mb-0"><?php echo e(auth()->user()->name); ?></h3>
                            </div>
                        </div>
                        <p class="text-white-50 mb-0 fs-5">
                            Lanjutkan perjalanan pembelajaran Anda dan raih tujuan yang telah ditetapkan.
                        </p>
                    </div>
                    <div class="col-md-4 text-end d-none d-md-block">
                        <div class="position-relative">
                            <i class="fas fa-graduation-cap text-white-50" style="font-size: 6rem; opacity: 0.3;"></i>
                            <div class="position-absolute top-0 end-0">
                                <div class="bg-white rounded-circle p-2" style="width: 40px; height: 40px;">
                                    <i class="fas fa-star" style="color: var(--light-green);"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card border-0 h-100" style="border-radius: 16px; background: linear-gradient(135deg, #fff 0%, var(--light-red) 100%);">
            <div class="card-body text-center p-4">
                <div class="mb-3">
                    <div class="d-inline-flex align-items-center justify-content-center rounded-circle"
                         style="width: 60px; height: 60px; background: var(--primary-red);">
                        <i class="fas fa-book-open fa-lg text-white"></i>
                    </div>
                </div>
                <h2 class="fw-bold mb-1" style="color: var(--primary-red);">
                    <?php echo e(auth()->user()->enrollments()->where('status', 'active')->count()); ?>

                </h2>
                <p class="text-muted mb-0 fw-semibold">Kursus Aktif</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-0 h-100" style="border-radius: 16px; background: linear-gradient(135deg, #fff 0%, #e8f5e8 100%);">
            <div class="card-body text-center p-4">
                <div class="mb-3">
                    <div class="d-inline-flex align-items-center justify-content-center rounded-circle"
                         style="width: 60px; height: 60px; background: var(--light-green);">
                        <i class="fas fa-check-circle fa-lg text-white"></i>
                    </div>
                </div>
                <h2 class="fw-bold mb-1" style="color: var(--light-green);">
                    <?php echo e(auth()->user()->enrollments()->where('status', 'completed')->count()); ?>

                </h2>
                <p class="text-muted mb-0 fw-semibold">Kursus Selesai</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-0 h-100" style="border-radius: 16px; background: linear-gradient(135deg, #fff 0%, #fff3cd 100%);">
            <div class="card-body text-center p-4">
                <div class="mb-3">
                    <div class="d-inline-flex align-items-center justify-content-center rounded-circle"
                         style="width: 60px; height: 60px; background: #ffc107;">
                        <i class="fas fa-star fa-lg text-white"></i>
                    </div>
                </div>
                <h2 class="fw-bold mb-1 text-warning">
                    <?php echo e(auth()->user()->reviews()->count()); ?>

                </h2>
                <p class="text-muted mb-0 fw-semibold">Review Diberikan</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card border-0 h-100" style="border-radius: 16px; background: linear-gradient(135deg, #fff 0%, #e1f5fe 100%);">
            <div class="card-body text-center p-4">
                <div class="mb-3">
                    <div class="d-inline-flex align-items-center justify-content-center rounded-circle"
                         style="width: 60px; height: 60px; background: var(--dark-teal);">
                        <i class="fas fa-credit-card fa-lg text-white"></i>
                    </div>
                </div>
                <h2 class="fw-bold mb-1" style="color: var(--dark-teal);">
                    <?php echo e(auth()->user()->payments()->where('status', 'verified')->count()); ?>

                </h2>
                <p class="text-muted mb-0 fw-semibold">Pembayaran Verified</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- My Courses -->
    <div class="col-md-8 mb-4">
        <div class="card border-0" style="border-radius: 20px;">
            <div class="card-header border-0 py-4" style="background: linear-gradient(135deg, var(--light-red) 0%, #fff 100%); border-radius: 20px 20px 0 0;">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1 fw-bold" style="color: var(--dark-teal);">
                            <i class="fas fa-book me-2"></i>Kursus Saya
                        </h4>
                        <p class="text-muted mb-0">Pantau progress pembelajaran Anda</p>
                    </div>
                    <a href="<?php echo e(route('courses.index')); ?>" class="btn btn-sm fw-semibold"
                       style="background: var(--primary-red); color: white; border-radius: 12px; padding: 0.5rem 1rem;">
                        <i class="fas fa-plus me-1"></i>Cari Kursus
                    </a>
                </div>
            </div>
            <div class="card-body p-4">
                <?php $__empty_1 = true; $__currentLoopData = auth()->user()->enrollments()->with('course')->latest()->take(5)->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $enrollment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="d-flex align-items-center mb-4 p-3 rounded-3" style="background: #f8f9fa; border-left: 4px solid var(--primary-red);">
                        <div class="me-3">
                            <div class="rounded-circle d-flex align-items-center justify-content-center"
                                 style="width: 50px; height: 50px; background: var(--light-red);">
                                <i class="fas fa-book" style="color: var(--primary-red);"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1 fw-semibold"><?php echo e($enrollment->course->title); ?></h6>
                            <div class="d-flex align-items-center gap-3 mb-2">
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    Bergabung: <?php echo e($enrollment->enrolled_at->format('d M Y')); ?>

                                </small>
                                <span class="badge rounded-pill"
                                      style="background: <?php echo e($enrollment->status == 'active' ? 'var(--light-green)' : ($enrollment->status == 'completed' ? 'var(--dark-teal)' : '#6c757d')); ?>; color: white;">
                                    <?php echo e(ucfirst($enrollment->status)); ?>

                                </span>
                            </div>
                            <!-- Progress Bar -->
                            <div class="progress" style="height: 6px; border-radius: 3px;">
                                <div class="progress-bar"
                                     style="background: var(--light-green); width: <?php echo e($enrollment->status == 'completed' ? '100' : ($enrollment->status == 'active' ? rand(20, 80) : '0')); ?>%;">
                                </div>
                            </div>
                        </div>
                        <div class="ms-3">
                            <a href="<?php echo e(route('courses.show', $enrollment->course)); ?>"
                               class="btn btn-outline-primary btn-sm rounded-pill">
                                <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="text-center py-5">
                        <div class="mb-4">
                            <div class="d-inline-flex align-items-center justify-content-center rounded-circle"
                                 style="width: 100px; height: 100px; background: var(--light-red);">
                                <i class="fas fa-book-open fa-2x" style="color: var(--primary-red);"></i>
                            </div>
                        </div>
                        <h5 class="mb-2" style="color: var(--dark-teal);">Belum Ada Kursus</h5>
                        <p class="text-muted mb-4">Mulai perjalanan pembelajaran Anda dengan bergabung di kursus pertama!</p>
                        <a href="<?php echo e(route('courses.index')); ?>" class="btn btn-lg fw-semibold"
                           style="background: var(--primary-red); color: white; border-radius: 12px; padding: 0.75rem 2rem;">
                            <i class="fas fa-search me-2"></i>Jelajahi Kursus
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="col-md-4 mb-4">
        <!-- Quick Actions -->
        <div class="card border-0" style="border-radius: 20px;">
            <div class="card-header border-0 py-4" style="background: linear-gradient(135deg, #e8f5e8 0%, #fff 100%); border-radius: 20px 20px 0 0;">
                <h5 class="mb-0 fw-bold" style="color: var(--dark-teal);">
                    <i class="fas fa-bolt me-2"></i>Aksi Cepat
                </h5>
            </div>
            <div class="card-body p-4">
                <div class="d-grid gap-3">
                    <a href="<?php echo e(route('courses.index')); ?>" class="btn btn-lg fw-semibold d-flex align-items-center"
                       style="background: var(--primary-red); color: white; border-radius: 12px; text-decoration: none;">
                        <i class="fas fa-search me-3"></i>
                        <span>Cari Kursus Baru</span>
                    </a>
                    <a href="<?php echo e(route('student.enrollments.index')); ?>" class="btn btn-lg fw-semibold d-flex align-items-center"
                       style="background: var(--light-green); color: white; border-radius: 12px; text-decoration: none;">
                        <i class="fas fa-book-open me-3"></i>
                        <span>Kelola Kursus</span>
                    </a>
                    <a href="<?php echo e(route('student.payments.index')); ?>" class="btn btn-lg fw-semibold d-flex align-items-center"
                       style="background: var(--dark-teal); color: white; border-radius: 12px; text-decoration: none;">
                        <i class="fas fa-credit-card me-3"></i>
                        <span>Riwayat Pembayaran</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Custom hover effects for dashboard */
.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.1);
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Progress bar animation */
.progress-bar {
    transition: width 1s ease-in-out;
}


</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\sistem-kursus\resources\views/student/dashboard.blade.php ENDPATH**/ ?>